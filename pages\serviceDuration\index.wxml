<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">服务时长统计</text>
    <view class="refresh-btn" bindtap="refresh">
      <text class="refresh-icon">🔄</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && !refreshing}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 当前进行中的服务列表 -->
  <view class="services-container" wx:elif="{{currentServices.length > 0 || availableOrders.length > 0}}">
    <!-- 进行中的服务 -->
    <view wx:if="{{currentServices.length > 0}}">
      <view class="section-title">
        <text class="title-text">进行中的服务</text>
        <text class="count-badge">{{currentServices.length}}</text>
      </view>

      <view class="service-list">
        <view class="service-item" wx:for="{{currentServices}}" wx:key="id">
          <!-- 服务信息 -->
          <view class="service-info" bindtap="viewOrderDetail" data-order-id="{{item.order.id}}">
            <view class="service-header">
              <view class="service-name-section">
                <text class="service-name">{{item.serviceName || item.additionalServiceName}}</text>
                <text class="service-type {{item.recordType === 'main_service' ? 'main' : 'additional'}}">{{item.recordType === 'main_service' ? '主服务' : '增项服务'}}</text>
              </view>
              <view class="duration-display">
                <text class="duration-time">{{item.currentDurationText}}</text>
              </view>
            </view>

            <!-- 订单信息 -->
            <view class="order-info">
              <view class="order-detail">
                <text class="order-label">订单号：</text>
                <text class="order-value">{{item.order.sn}}</text>
              </view>
              <view class="order-detail">
                <text class="order-label">状态：</text>
                <text class="order-value status">{{item.order.status}}</text>
              </view>
            </view>

            <!-- 时间信息 -->
            <view class="time-info">
              <view class="time-detail">
                <text class="time-label">开始时间：</text>
                <text class="time-value">{{item.startTime}}</text>
              </view>
            </view>

            <!-- 服务信息 -->
            <view class="service-detail" wx:if="{{item.service}}">
              <text class="service-label">服务项目：</text>
              <text class="service-value">{{item.service.serviceName}}</text>
            </view>

            <!-- 备注信息 -->
            <view class="remark-info" wx:if="{{item.remark}}">
              <text class="remark-label">备注：</text>
              <text class="remark-value">{{item.remark}}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="service-actions">
            <view class="action-btn end-btn"
                  bindtap="endService"
                  data-record-id="{{item.id}}"
                  data-service-name="{{item.serviceName || item.additionalServiceName}}">
              <text class="btn-text">结束计时</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 可开始服务的订单 -->
    <view wx:if="{{availableOrders.length > 0}}">
      <view class="section-title" style="margin-top: {{currentServices.length > 0 ? '40rpx' : '0'}};">
        <text class="title-text">待开始服务的订单</text>
        <text class="count-badge available">{{availableOrders.length}}</text>
      </view>

      <view class="order-cards">
        <view class="order-card" wx:for="{{availableOrders}}" wx:key="id" wx:for-item="order"
              bindtap="viewOrderDetail" data-order-id="{{order.id}}">
          <!-- 订单头部信息 -->
          <view class="order-card-header">
            <view class="order-info-row">
              <text class="order-sn">{{order.sn}}</text>
              <text class="order-status">{{order.status}}</text>
            </view>
            <view class="customer-info" wx:if="{{order.customer}}">
              <text class="customer-name">{{order.customer.nickname || '客户'}}</text>
              <text class="customer-phone" wx:if="{{order.customer.phone}}">({{order.customer.phone}})</text>
            </view>
          </view>

          <!-- 服务概览 -->
          <view class="services-overview">
            <view class="service-summary">
              <view class="summary-item" wx:if="{{order.mainServices && order.mainServices.length > 0}}">
                <text class="summary-label">主服务：</text>
                <text class="summary-count">{{order.mainServices.length}}项待开始</text>
              </view>
              <view class="summary-item" wx:if="{{order.additionalServices && order.additionalServices.length > 0}}">
                <text class="summary-label">增项服务：</text>
                <text class="summary-count">{{order.additionalServices.length}}项待开始</text>
              </view>
            </view>

            <!-- 服务列表预览 -->
            <view class="service-preview">
              <view class="preview-services">
                <text class="preview-item" wx:for="{{order.mainServices}}" wx:key="orderDetailId" wx:for-item="service">
                  {{service.serviceName}}
                </text>
                <text class="preview-item additional" wx:for="{{order.additionalServices}}" wx:key="additionalServiceOrderId" wx:for-item="service">
                  {{service.serviceName}}
                </text>
              </view>
            </view>
          </view>

          <!-- 操作提示 -->
          <view class="card-action-hint">
            <text class="hint-text">点击查看详情并开始服务</text>
            <text class="hint-arrow">→</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:else>
    <view class="empty-icon">⏱️</view>
    <text class="empty-title">暂无服务项目</text>
    <text class="empty-desc">当前没有进行中或可开始的服务项目</text>
    <view class="empty-action">
      <view class="action-btn refresh-btn" bindtap="refresh">
        <text class="btn-text">刷新页面</text>
      </view>
    </view>
  </view>

  <!-- 温馨提示 -->
  <view class="tips-container" wx:if="{{currentServices.length > 0 || availableOrders.length > 0}}">
    <view class="tips-header">
      <text class="tips-title">💡 温馨提示</text>
    </view>
    <view class="tips-content">
      <text class="tips-item">• 服务时长会自动计算，每分钟更新一次</text>
      <text class="tips-item">• 点击服务卡片可查看订单详情</text>
      <text class="tips-item">• 开始服务前请确认服务内容</text>
      <text class="tips-item">• 完成服务后请及时点击"结束计时"</text>
      <text class="tips-item">• 时长统计有助于优化服务效率</text>
    </view>
  </view>
</view>
