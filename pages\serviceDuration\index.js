import serviceDurationApi from '../../api/modules/serviceDuration';
import { formatNormalDate } from '../utils/util';
import Session from '../../common/Session';

Page({
  data: {
    userInfo: null,
    currentServices: [], // 当前进行中的服务
    loading: false,
    refreshing: false,
    timer: null, // 定时器
  },

  onLoad() {
    const userInfo = Session.getUser();
    this.setData({ userInfo });
    this.loadCurrentServices();
    this.startTimer();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadCurrentServices();
  },

  onUnload() {
    // 页面卸载时清除定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },

  // 加载当前进行中的服务
  async loadCurrentServices() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const result = await serviceDurationApi.getCurrent();

      // 新接口返回格式：{ employeeId, currentServices, totalCurrentServices }
      const services = result?.currentServices || [];

      if (services && Array.isArray(services)) {
        const formattedServices = services.map(service => ({
          ...service,
          startTime: service.startTime ? formatNormalDate(service.startTime) : null,
          startTimeObj: service.startTime ? new Date(service.startTime) : null,
          currentDuration: this.calculateCurrentDuration(service.startTime),
          currentDurationText: this.formatDuration(this.calculateCurrentDuration(service.startTime)),
          // 确保所有关联对象都正确处理
          order: service.order || {},
          service: service.service || {},
          orderDetail: service.orderDetail || {},
          additionalService: service.additionalService || {},
        }));

        this.setData({
          currentServices: formattedServices,
        });
      }
    } catch (error) {
      console.error('加载当前服务失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error',
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.setData({ refreshing: true });
    await this.loadCurrentServices();
    this.setData({ refreshing: false });
    wx.stopPullDownRefresh();
  },

  // 计算当前服务时长（分钟）
  calculateCurrentDuration(startTime) {
    if (!startTime) return 0;
    const start = new Date(startTime);
    const now = new Date();
    return Math.floor((now - start) / (1000 * 60));
  },

  // 格式化时长显示
  formatDuration(minutes) {
    if (!minutes || minutes < 0) return '0分钟';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}小时${mins}分钟`;
    }
    return `${mins}分钟`;
  },

  // 启动定时器，每分钟更新一次时长显示
  startTimer() {
    const timer = setInterval(() => {
      this.updateCurrentDuration();
    }, 60000); // 60秒更新一次

    this.setData({ timer });
  },

  // 更新当前服务时长显示
  updateCurrentDuration() {
    const { currentServices } = this.data;
    if (currentServices.length === 0) return;

    const updatedServices = currentServices.map(service => ({
      ...service,
      currentDuration: this.calculateCurrentDuration(service.startTime),
      currentDurationText: this.formatDuration(this.calculateCurrentDuration(service.startTime)),
    }));

    this.setData({
      currentServices: updatedServices,
    });
  },

  // 结束服务时长统计
  async endService(e) {
    const { recordId, serviceName } = e.currentTarget.dataset;

    wx.showModal({
      title: '确认结束',
      content: `确定要结束"${serviceName}"的服务时长统计吗？`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
          });

          try {
            const params = {
              recordId: recordId,
              remark: `完成${serviceName}服务`,
            };

            const result = await serviceDurationApi.end(params);
            if (result) {
              wx.showToast({
                title: '已结束计时',
                icon: 'success',
              });
              // 重新加载数据
              this.loadCurrentServices();
            } else {
              wx.showToast({
                title: '操作失败',
                icon: 'error',
              });
            }
          } catch (error) {
            console.error('结束服务时长统计失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'error',
            });
          } finally {
            wx.hideLoading();
          }
        }
      },
    });
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const { orderId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/orders/orderDetail/index?orderId=${orderId}`,
    });
  },

  // 手动刷新
  refresh() {
    this.loadCurrentServices();
  },
});
